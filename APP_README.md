# 📈 股票龙虎榜与资金流数据分析应用

基于qstock库开发的股票龙虎榜与资金流数据分析工具，提供多种界面选择，帮助用户分析股票市场的异常交易和资金流向。

## 🌟 功能特性

- **龙虎榜数据获取**: 获取指定日期的股票龙虎榜数据
- **资金流分析**: 分析龙虎榜股票的主力资金流入流出情况
- **多周期分析**: 支持1日、3日、5日、10日、20日、30日、60日等多个周期
- **可视化展示**: 提供直观的图表展示资金流趋势
- **多界面支持**: Web界面、桌面界面、命令行界面

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install qstock pandas streamlit plotly matplotlib
```

### 2. 运行应用

#### 方式一：使用启动器（推荐）
```bash
python run_app.py
```

#### 方式二：直接运行特定界面
```bash
# Web界面（推荐）
streamlit run dragon_tiger_board.py

# 桌面界面
python dragon_tiger_tkinter.py

# 命令行演示
python simple_dragon_tiger.py
```

#### 方式三：命令行参数
```bash
# 直接启动Web界面
python run_app.py --mode web

# 直接启动桌面界面
python run_app.py --mode desktop

# 直接运行命令行演示
python run_app.py --mode cli

# 检查依赖包
python run_app.py --check-deps
```

## 📱 界面介绍

### 🌐 Streamlit Web界面 (推荐)

**文件**: `dragon_tiger_board.py`

**特点**:
- 现代化的Web界面，功能最完整
- 支持交互式图表和数据展示
- 自动在浏览器中打开
- 响应式设计，支持移动设备

**功能**:
- 侧边栏日期和参数设置
- 龙虎榜数据统计展示
- 多股票资金流对比分析
- 交互式Plotly图表
- 详细的使用说明

### 🖥️ Tkinter桌面界面

**文件**: `dragon_tiger_tkinter.py`

**特点**:
- 传统的桌面应用界面
- 无需浏览器，独立运行
- 支持基本的数据查询和图表显示
- 适合离线使用

**功能**:
- 日期和参数设置面板
- 龙虎榜数据表格显示
- 股票选择和资金流分析
- Matplotlib图表展示

### 💻 命令行演示

**文件**: `simple_dragon_tiger.py`

**特点**:
- 简单的命令行脚本
- 快速演示基本功能
- 适合自动化和批处理

**功能**:
- 获取指定日期龙虎榜数据
- 批量分析所有龙虎榜股票的资金流
- 基本的图表展示

## 📊 使用说明

### 基本流程

1. **选择查询日期**: 选择要查询龙虎榜数据的日期（必须是交易日）
2. **获取龙虎榜数据**: 点击查询按钮获取当日龙虎榜数据
3. **选择分析股票**: 从龙虎榜股票中选择要分析的股票
4. **设置分析参数**: 选择资金流统计周期
5. **查看分析结果**: 查看资金流数据和图表

### 参数说明

- **查询日期**: 格式为YYYYMMDD，如20231020
- **资金流周期**: 支持1、3、5、10、20、30、60日等周期
- **股票选择**: 可以选择单只或多只股票进行对比分析

### 数据说明

- **主力净流入**: 当日主力资金净流入金额（万元）
- **N日累计**: 近N日主力资金净流入累计金额（万元）
- **正值**: 表示资金净流入（红色显示）
- **负值**: 表示资金净流出（绿色显示）

## 🔧 技术架构

### 核心依赖

- **qstock**: 数据获取核心库
- **pandas**: 数据处理
- **streamlit**: Web界面框架
- **plotly**: 交互式图表
- **matplotlib**: 静态图表
- **tkinter**: 桌面界面框架

### 文件结构

```
├── dragon_tiger_board.py      # Streamlit Web界面
├── dragon_tiger_tkinter.py    # Tkinter桌面界面
├── simple_dragon_tiger.py     # 命令行演示
├── run_app.py                 # 应用启动器
├── APP_README.md              # 应用说明文档
└── README.md                  # qstock库说明文档
```

## ⚠️ 注意事项

1. **交易日限制**: 只能查询交易日的龙虎榜数据，周末和节假日无数据
2. **网络连接**: 需要稳定的网络连接获取实时数据
3. **数据延迟**: 数据可能存在一定延迟，以官方公布为准
4. **投资风险**: 数据仅供参考，不构成投资建议

## 🐛 常见问题

### Q: 提示"没有找到龙虎榜数据"怎么办？
A: 请检查：
- 选择的日期是否为交易日
- 网络连接是否正常
- 该日期是否确实有龙虎榜数据

### Q: 图表显示异常怎么办？
A: 请检查：
- 是否安装了所有依赖包
- 浏览器是否支持JavaScript
- 尝试刷新页面或重启应用

### Q: 如何获取更多历史数据？
A: 可以修改代码中的日期范围，或者使用qstock库的其他接口获取历史数据

## 📞 技术支持

- **qstock库文档**: 参考README.md
- **问题反馈**: 通过GitHub Issues反馈
- **功能建议**: 欢迎提出改进建议

## 📄 许可证

本项目基于qstock开源库开发，遵循相应的开源协议。

---

**免责声明**: 本工具仅供学习和研究使用，所提供的数据和分析结果仅供参考，不构成任何投资建议。投资有风险，决策需谨慎。
