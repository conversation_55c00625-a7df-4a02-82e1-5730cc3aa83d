import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from tkinter.ttk import Progressbar
import qstock as qs
import pandas as pd
from datetime import datetime, timedelta
import threading
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
from matplotlib.figure import Figure

class DragonTigerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("股票龙虎榜与资金流数据分析")
        self.root.geometry("1200x800")
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.setup_ui()
        
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="股票龙虎榜与资金流数据分析", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="查询设置", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 日期选择
        ttk.Label(control_frame, text="查询日期:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.date_var = tk.StringVar()
        today = datetime.today()
        # 默认选择最近的交易日
        if today.weekday() >= 5:  # 周末
            default_date = today - timedelta(days=today.weekday()-4)
        else:
            default_date = today
        self.date_var.set(default_date.strftime('%Y%m%d'))
        
        date_entry = ttk.Entry(control_frame, textvariable=self.date_var, width=15)
        date_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 资金流周期选择
        ttk.Label(control_frame, text="资金流周期:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.money_days_var = tk.StringVar(value="5")
        money_combo = ttk.Combobox(control_frame, textvariable=self.money_days_var, 
                                  values=["1", "3", "5", "10", "20", "30", "60"], width=12)
        money_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 查询按钮
        self.query_btn = ttk.Button(control_frame, text="获取龙虎榜数据", 
                                   command=self.query_billboard)
        self.query_btn.grid(row=2, column=0, columnspan=2, pady=20, sticky=(tk.W, tk.E))
        
        # 进度条
        self.progress = Progressbar(control_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 股票选择
        ttk.Label(control_frame, text="选择股票:").grid(row=4, column=0, sticky=tk.W, pady=(20, 5))
        self.stock_listbox = tk.Listbox(control_frame, height=8, selectmode=tk.MULTIPLE)
        self.stock_listbox.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(control_frame, orient=tk.VERTICAL, command=self.stock_listbox.yview)
        scrollbar.grid(row=5, column=2, sticky=(tk.N, tk.S), pady=5)
        self.stock_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 分析按钮
        self.analyze_btn = ttk.Button(control_frame, text="分析资金流", 
                                     command=self.analyze_money_flow, state=tk.DISABLED)
        self.analyze_btn.grid(row=6, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))
        
        # 右侧显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.grid(row=1, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        display_frame.columnconfigure(0, weight=1)
        display_frame.rowconfigure(1, weight=1)
        
        # 创建Notebook用于标签页
        self.notebook = ttk.Notebook(display_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 龙虎榜数据标签页
        self.billboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.billboard_frame, text="龙虎榜数据")
        
        # 创建Treeview显示龙虎榜数据
        self.billboard_tree = ttk.Treeview(self.billboard_frame, show='headings', height=15)
        self.billboard_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加滚动条
        billboard_scrollbar_y = ttk.Scrollbar(self.billboard_frame, orient=tk.VERTICAL, 
                                            command=self.billboard_tree.yview)
        billboard_scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.billboard_tree.configure(yscrollcommand=billboard_scrollbar_y.set)
        
        billboard_scrollbar_x = ttk.Scrollbar(self.billboard_frame, orient=tk.HORIZONTAL, 
                                            command=self.billboard_tree.xview)
        billboard_scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.billboard_tree.configure(xscrollcommand=billboard_scrollbar_x.set)
        
        # 配置网格权重
        self.billboard_frame.columnconfigure(0, weight=1)
        self.billboard_frame.rowconfigure(0, weight=1)
        
        # 资金流分析标签页
        self.money_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.money_frame, text="资金流分析")
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(display_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 存储数据
        self.billboard_data = None
        self.stock_dict = {}
        
    def query_billboard(self):
        """查询龙虎榜数据"""
        date_str = self.date_var.get()
        if not date_str or len(date_str) != 8:
            messagebox.showerror("错误", "请输入正确的日期格式 (YYYYMMDD)")
            return
            
        # 在新线程中执行查询
        thread = threading.Thread(target=self._query_billboard_thread, args=(date_str,))
        thread.daemon = True
        thread.start()
        
    def _query_billboard_thread(self, date_str):
        """在后台线程中查询龙虎榜数据"""
        try:
            self.root.after(0, lambda: self.progress.start())
            self.root.after(0, lambda: self.status_var.set("正在获取龙虎榜数据..."))
            self.root.after(0, lambda: self.query_btn.config(state=tk.DISABLED))
            
            # 获取龙虎榜数据
            df_billboard = qs.stock_billboard(date_str, date_str)
            
            self.root.after(0, lambda: self._update_billboard_display(df_billboard, date_str))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"获取数据失败: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.progress.stop())
            self.root.after(0, lambda: self.query_btn.config(state=tk.NORMAL))
            
    def _update_billboard_display(self, df_billboard, date_str):
        """更新龙虎榜显示"""
        if df_billboard.empty:
            self.status_var.set(f"没有找到{date_str}的龙虎榜数据")
            messagebox.showwarning("提示", f"没有找到{date_str}的龙虎榜数据，请选择其他日期")
            return
            
        self.billboard_data = df_billboard
        self.status_var.set(f"成功获取{date_str}的龙虎榜数据，共{len(df_billboard)}只股票")
        
        # 清空现有数据
        for item in self.billboard_tree.get_children():
            self.billboard_tree.delete(item)
            
        # 设置列
        columns = list(df_billboard.columns)
        self.billboard_tree['columns'] = columns
        
        # 设置列标题和宽度
        for col in columns:
            self.billboard_tree.heading(col, text=col)
            self.billboard_tree.column(col, width=100, minwidth=80)
            
        # 插入数据
        for index, row in df_billboard.iterrows():
            self.billboard_tree.insert('', 'end', values=list(row))
            
        # 更新股票列表
        if '代码' in df_billboard.columns and '名称' in df_billboard.columns:
            self.stock_listbox.delete(0, tk.END)
            self.stock_dict = {}
            
            for index, row in df_billboard.iterrows():
                code = row['代码']
                name = row['名称']
                display_text = f"{code} - {name}"
                self.stock_listbox.insert(tk.END, display_text)
                self.stock_dict[display_text] = code
                
            self.analyze_btn.config(state=tk.NORMAL)
            
    def analyze_money_flow(self):
        """分析选中股票的资金流"""
        selected_indices = self.stock_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("提示", "请先选择要分析的股票")
            return
            
        selected_stocks = [self.stock_listbox.get(i) for i in selected_indices]
        money_days = int(self.money_days_var.get())
        
        # 在新线程中执行分析
        thread = threading.Thread(target=self._analyze_money_flow_thread, 
                                 args=(selected_stocks, money_days))
        thread.daemon = True
        thread.start()
        
    def _analyze_money_flow_thread(self, selected_stocks, money_days):
        """在后台线程中分析资金流"""
        try:
            self.root.after(0, lambda: self.progress.start())
            self.root.after(0, lambda: self.status_var.set("正在分析资金流数据..."))
            self.root.after(0, lambda: self.analyze_btn.config(state=tk.DISABLED))
            
            money_data = {}
            for stock_display in selected_stocks:
                stock_code = self.stock_dict[stock_display]
                df_money = qs.stock_money(stock_code, ndays=[money_days])
                money_data[stock_display] = df_money
                
            self.root.after(0, lambda: self._update_money_display(money_data, money_days))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析资金流失败: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.progress.stop())
            self.root.after(0, lambda: self.analyze_btn.config(state=tk.NORMAL))
            
    def _update_money_display(self, money_data, money_days):
        """更新资金流显示"""
        # 清空资金流框架
        for widget in self.money_frame.winfo_children():
            widget.destroy()
            
        if not money_data:
            ttk.Label(self.money_frame, text="没有资金流数据").pack(pady=20)
            return
            
        # 创建滚动框架
        canvas = tk.Canvas(self.money_frame)
        scrollbar = ttk.Scrollbar(self.money_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 为每只股票创建图表
        for i, (stock_name, df_money) in enumerate(money_data.items()):
            if df_money.empty:
                continue
                
            # 创建股票标题
            stock_frame = ttk.LabelFrame(scrollable_frame, text=stock_name, padding="10")
            stock_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # 创建matplotlib图表
            fig = Figure(figsize=(10, 4), dpi=80)
            ax1 = fig.add_subplot(121)
            ax2 = fig.add_subplot(122)
            
            # 绘制主力净流入柱状图
            dates = pd.to_datetime(df_money.index)
            values = df_money.iloc[:, 0]  # 第一列是主力净流入
            colors = ['red' if x > 0 else 'green' for x in values]
            ax1.bar(dates, values, color=colors, alpha=0.7)
            ax1.set_title('主力净流入趋势')
            ax1.set_ylabel('净流入(万元)')
            ax1.tick_params(axis='x', rotation=45)
            
            # 绘制累计资金流线图
            if f"{money_days}日主力净流入" in df_money.columns:
                cumulative = df_money[f"{money_days}日主力净流入"]
                ax2.plot(dates, cumulative, marker='o', linewidth=2)
                ax2.set_title(f'{money_days}日累计资金流')
                ax2.set_ylabel('累计净流入(万元)')
                ax2.tick_params(axis='x', rotation=45)
                ax2.grid(True, alpha=0.3)
            
            fig.tight_layout()
            
            # 将图表嵌入到tkinter中
            canvas_widget = FigureCanvasTkAgg(fig, stock_frame)
            canvas_widget.draw()
            canvas_widget.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.status_var.set(f"资金流分析完成，共分析{len(money_data)}只股票")

def main():
    root = tk.Tk()
    app = DragonTigerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
