#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票龙虎榜与资金流数据分析应用启动器

提供多种界面选择：
1. Streamlit Web界面 (推荐)
2. Tkinter桌面界面
3. 命令行界面

作者: qstock用户
"""

import sys
import os
import subprocess
import argparse
from datetime import datetime

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = ['qstock', 'pandas', 'streamlit', 'plotly', 'matplotlib']

    missing_packages = []

    for package in required_packages:
        try:
            if package == 'qstock':
                # 简单检查qstock是否可导入，不执行复杂操作
                import importlib
                spec = importlib.util.find_spec(package)
                if spec is None:
                    missing_packages.append(package)
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
        except Exception as e:
            # 如果导入时出现其他错误（如版本兼容性），给出警告但不阻止运行
            print(f"⚠️  {package} 导入时出现警告: {e}")

    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ 依赖包检查完成")
    return True

def run_streamlit_app():
    """运行Streamlit Web界面"""
    print("🚀 启动Streamlit Web界面...")
    print("📝 提示: 应用将在浏览器中自动打开")
    print("🔗 如果没有自动打开，请访问: http://localhost:8501")
    print("⏹️  按 Ctrl+C 停止应用\n")

    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "dragon_tiger_board.py",
            "--server.headless", "false",
            "--server.port", "8501"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_tkinter_app():
    """运行Tkinter桌面界面"""
    print("🖥️  启动Tkinter桌面界面...")
    try:
        import dragon_tiger_tkinter
        dragon_tiger_tkinter.main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_simple_demo():
    """运行简单命令行演示"""
    print("💻 运行命令行演示...")
    try:
        import simple_dragon_tiger
        print("✅ 演示完成")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def show_menu():
    """显示菜单"""
    print("=" * 60)
    print("📈 股票龙虎榜与资金流数据分析")
    print("=" * 60)
    print("请选择运行方式:")
    print("1. 🌐 Streamlit Web界面 (推荐)")
    print("2. 🖥️  Tkinter桌面界面")
    print("3. 💻 命令行演示")
    print("4. ❓ 查看帮助")
    print("0. 🚪 退出")
    print("=" * 60)

def show_help():
    """显示帮助信息"""
    help_text = """
📖 使用说明

🌐 Streamlit Web界面 (推荐)
   - 现代化的Web界面，功能最完整
   - 支持交互式图表和数据展示
   - 自动在浏览器中打开
   - 适合数据分析和可视化

🖥️ Tkinter桌面界面
   - 传统的桌面应用界面
   - 无需浏览器，独立运行
   - 支持基本的数据查询和图表显示
   - 适合离线使用

💻 命令行演示
   - 简单的命令行脚本
   - 快速演示基本功能
   - 适合自动化和批处理

📊 功能特性
   - 获取指定日期的股票龙虎榜数据
   - 分析龙虎榜股票的资金流情况
   - 支持多周期资金流分析 (1日、3日、5日、10日、20日等)
   - 提供直观的图表展示
   - 数据来源于公开市场信息

⚠️ 注意事项
   - 请选择交易日进行查询
   - 数据仅供参考，不构成投资建议
   - 确保网络连接正常

🔧 技术支持
   - 基于qstock开源库开发
   - 支持Python 3.7+
   - 开源免费使用
"""
    print(help_text)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票龙虎榜与资金流数据分析')
    parser.add_argument('--mode', choices=['web', 'desktop', 'cli'],
                       help='运行模式: web(Streamlit), desktop(Tkinter), cli(命令行)')
    parser.add_argument('--check-deps', action='store_true', help='检查依赖包')

    args = parser.parse_args()

    # 检查依赖
    if args.check_deps:
        check_dependencies()
        return

    if not check_dependencies():
        return

    # 直接运行指定模式
    if args.mode == 'web':
        run_streamlit_app()
        return
    elif args.mode == 'desktop':
        run_tkinter_app()
        return
    elif args.mode == 'cli':
        run_simple_demo()
        return

    # 交互式菜单
    while True:
        show_menu()
        try:
            choice = input("请输入选择 (0-4): ").strip()

            if choice == '1':
                run_streamlit_app()
            elif choice == '2':
                run_tkinter_app()
            elif choice == '3':
                run_simple_demo()
            elif choice == '4':
                show_help()
            elif choice == '0':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
