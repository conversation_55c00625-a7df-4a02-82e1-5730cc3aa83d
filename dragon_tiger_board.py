import qstock as qs
import pandas as pd
from datetime import datetime, timedelta
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 设置页面配置
st.set_page_config(
    page_title="股票龙虎榜与资金流数据分析",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 创建侧边栏
st.sidebar.title("📊 数据查询设置")

# 主标题
st.title('📈 股票龙虎榜与资金流数据分析')
st.markdown("---")

# 侧边栏日期选择
st.sidebar.subheader("📅 日期设置")
today = datetime.today().date()
# 默认选择最近的交易日（避免周末）
if today.weekday() >= 5:  # 周六或周日
    default_date = today - timedelta(days=today.weekday()-4)
else:
    default_date = today

selected_date = st.sidebar.date_input(
    '选择查询日期',
    value=default_date,
    max_value=today,
    help="选择要查询龙虎榜数据的日期"
)
date_str = selected_date.strftime('%Y%m%d')

# 侧边栏资金流设置
st.sidebar.subheader("💰 资金流设置")
money_days = st.sidebar.multiselect(
    '选择资金流统计周期',
    options=[1, 3, 5, 10, 20, 30, 60],
    default=[5],
    help="选择要查看的资金流统计周期（天数）"
)

# 显示图表选项
show_charts = st.sidebar.checkbox("显示图表", value=True, help="是否显示资金流图表")

# 主界面
col1, col2 = st.columns([2, 1])

with col1:
    st.subheader(f"🎯 {selected_date.strftime('%Y年%m月%d日')} 龙虎榜数据")

with col2:
    if st.button('🔍 获取龙虎榜数据', type="primary", use_container_width=True):
        # 获取指定日期的龙虎榜数据
        with st.spinner('正在获取龙虎榜数据...'):
            try:
                df_billboard = qs.stock_billboard(date_str, date_str)
            except Exception as e:
                st.error(f"获取数据时出错: {e}")
                df_billboard = pd.DataFrame()

        if df_billboard.empty:
            st.warning(f'⚠️ 没有找到{date_str}的龙虎榜数据，请选择其他日期（可能是非交易日）')
        else:
            st.success(f'✅ 成功获取{date_str}的龙虎榜数据，共{len(df_billboard)}只股票')

            # 显示龙虎榜数据统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("上榜股票数", len(df_billboard))
            with col2:
                if '涨跌幅' in df_billboard.columns:
                    avg_change = df_billboard['涨跌幅'].mean()
                    st.metric("平均涨跌幅", f"{avg_change:.2f}%")
            with col3:
                if '涨跌幅' in df_billboard.columns:
                    up_count = len(df_billboard[df_billboard['涨跌幅'] > 0])
                    st.metric("上涨股票数", up_count)
            with col4:
                if '涨跌幅' in df_billboard.columns:
                    down_count = len(df_billboard[df_billboard['涨跌幅'] < 0])
                    st.metric("下跌股票数", down_count)

            # 显示龙虎榜数据表格
            st.dataframe(
                df_billboard,
                use_container_width=True,
                height=400
            )

            # 获取龙虎榜股票列表
            if '代码' in df_billboard.columns and '名称' in df_billboard.columns:
                stock_list = df_billboard['代码'].tolist()
                stock_names = df_billboard['名称'].tolist()

                # 创建股票代码和名称的映射
                stock_dict = dict(zip(stock_list, stock_names))

                st.markdown("---")
                st.subheader("💰 选择股票查看资金流数据")

                # 选择要查看资金流的股票
                selected_stocks = st.multiselect(
                    '选择要查看资金流的股票',
                    options=stock_list,
                    format_func=lambda x: f"{x} - {stock_dict[x]}",
                    help="可以选择多只股票同时查看资金流数据"
                )

                if selected_stocks:
                    # 获取选中股票的资金流数据
                    with st.spinner('正在获取资金流数据...'):
                        for i, stock in enumerate(selected_stocks):
                            st.markdown("---")
                            st.subheader(f"📊 {stock} - {stock_dict[stock]} 资金流数据")

                            try:
                                # 获取资金流数据
                                df_money = qs.stock_money(stock, ndays=money_days)

                                if not df_money.empty:
                                    # 显示最新资金流数据
                                    latest_data = df_money.iloc[-1]

                                    # 创建指标展示
                                    cols = st.columns(len(money_days) + 1)
                                    with cols[0]:
                                        st.metric(
                                            "最新主力净流入",
                                            f"{latest_data.iloc[0]:.2f}万元",
                                            help="当日主力净流入金额"
                                        )

                                    for j, day in enumerate(money_days):
                                        if f"{day}日主力净流入" in df_money.columns:
                                            with cols[j+1]:
                                                value = latest_data[f"{day}日主力净流入"]
                                                st.metric(
                                                    f"{day}日累计",
                                                    f"{value:.2f}万元",
                                                    help=f"近{day}日主力净流入累计金额"
                                                )

                                    # 显示资金流数据表格
                                    st.dataframe(
                                        df_money.tail(20),  # 显示最近20天的数据
                                        use_container_width=True
                                    )

                                    # 可视化资金流数据
                                    if show_charts:
                                        st.write("📈 资金流趋势图:")

                                        # 创建子图
                                        fig = make_subplots(
                                            rows=2, cols=1,
                                            subplot_titles=('主力净流入趋势', '累计资金流趋势'),
                                            vertical_spacing=0.1,
                                            row_heights=[0.4, 0.6]
                                        )

                                        # 添加主力净流入柱状图
                                        fig.add_trace(
                                            go.Bar(
                                                x=df_money.index,
                                                y=df_money.iloc[:, 0],  # 第一列是主力净流入
                                                name="主力净流入",
                                                marker_color=['red' if x > 0 else 'green' for x in df_money.iloc[:, 0]]
                                            ),
                                            row=1, col=1
                                        )

                                        # 添加累计资金流线图
                                        for j, day in enumerate(money_days):
                                            col_name = f"{day}日主力净流入"
                                            if col_name in df_money.columns:
                                                fig.add_trace(
                                                    go.Scatter(
                                                        x=df_money.index,
                                                        y=df_money[col_name],
                                                        mode='lines+markers',
                                                        name=f"{day}日累计",
                                                        line=dict(width=2)
                                                    ),
                                                    row=2, col=1
                                                )

                                        # 更新布局
                                        fig.update_layout(
                                            title=f"{stock_dict[stock]} 资金流分析",
                                            height=600,
                                            showlegend=True,
                                            hovermode='x unified'
                                        )

                                        fig.update_xaxes(title_text="日期", row=2, col=1)
                                        fig.update_yaxes(title_text="净流入(万元)", row=1, col=1)
                                        fig.update_yaxes(title_text="累计净流入(万元)", row=2, col=1)

                                        st.plotly_chart(fig, use_container_width=True)

                                else:
                                    st.warning(f"⚠️ 无法获取 {stock} 的资金流数据")

                            except Exception as e:
                                st.error(f"❌ 获取 {stock} 资金流数据时出错: {e}")
            else:
                st.error("❌ 龙虎榜数据格式异常，缺少必要的列")

# 添加使用说明
with st.expander("📖 使用说明"):
    st.markdown("""
    ### 功能说明
    1. **龙虎榜数据**: 获取指定日期的股票龙虎榜数据，显示当日异常交易的股票信息
    2. **资金流分析**: 基于龙虎榜股票，分析其主力资金流入流出情况
    3. **多周期分析**: 支持1日、3日、5日、10日、20日、30日、60日等多个周期的资金流分析
    4. **可视化展示**: 提供直观的图表展示资金流趋势

    ### 使用步骤
    1. 在左侧选择要查询的日期
    2. 设置资金流统计周期
    3. 点击"获取龙虎榜数据"按钮
    4. 从龙虎榜股票中选择要分析的股票
    5. 查看详细的资金流数据和图表

    ### 注意事项
    - 请选择交易日进行查询
    - 数据来源于公开市场信息
    - 资金流数据仅供参考，不构成投资建议
    """)

# 页脚
st.markdown("---")
st.markdown(
    "<div style='text-align: center; color: gray;'>基于 qstock 库开发 | 数据仅供参考</div>",
    unsafe_allow_html=True
)