import qstock as qs
import pandas as pd
from datetime import datetime
import streamlit as st

# 创建Streamlit应用界面
st.title('股票龙虎榜与资金流数据分析')

# 日期选择器
today = datetime.today().strftime('%Y-%m-%d')
selected_date = st.date_input('选择日期', value=pd.to_datetime(today))
date_str = selected_date.strftime('%Y%m%d')

if st.button('获取龙虎榜数据'):
    # 获取指定日期的龙虎榜数据
    with st.spinner('正在获取龙虎榜数据...'):
        df_billboard = qs.stock_billboard(date_str, date_str)
        
    if df_billboard.empty:
        st.warning(f'没有找到{date_str}的龙虎榜数据，请选择其他日期')
    else:
        st.success(f'成功获取{date_str}的龙虎榜数据')
        st.dataframe(df_billboard)
        
        # 获取龙虎榜股票列表
        stock_list = df_billboard['代码'].tolist()
        stock_names = df_billboard['名称'].tolist()
        
        # 创建股票代码和名称的映射
        stock_dict = dict(zip(stock_list, stock_names))
        
        # 选择要查看资金流的股票
        selected_stocks = st.multiselect('选择要查看资金流的股票', 
                                         options=stock_list,
                                         format_func=lambda x: f"{x} - {stock_dict[x]}")
        
        if selected_stocks:
            # 获取选中股票的5日资金流数据
            with st.spinner('正在获取资金流数据...'):
                for stock in selected_stocks:
                    st.subheader(f"{stock} - {stock_dict[stock]}的资金流数据")
                    
                    # 获取5日资金流数据
                    df_money = qs.stock_money(stock, ndays=[5])
                    
                    # 显示资金流数据
                    st.dataframe(df_money)
                    
                    # 可视化资金流数据
                    st.write("资金流可视化:")
                    try:
                        # 使用qstock的plot模块可视化资金流
                        from qstock import plot
                        fig = plot.line(df_money, title=f"{stock_dict[stock]}资金流")
                        st.pyplot(fig)
                    except Exception as e:
                        st.error(f"可视化出错: {e}")
                        # 备选方案：使用streamlit的图表功能
                        st.line_chart(df_money)