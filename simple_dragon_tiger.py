import qstock as qs
import pandas as pd
from datetime import datetime

# 指定日期
date_str = '20221020'  # 格式：YYYYMMDD

# 获取指定日期的龙虎榜数据
df_billboard = qs.stock_billboard(date_str, date_str)
print(f"龙虎榜数据：\n{df_billboard}\n")

# 获取龙虎榜股票的5日资金流数据
if not df_billboard.empty:
    for index, row in df_billboard.iterrows():
        stock_code = row['代码']
        stock_name = row['名称']
        print(f"\n获取 {stock_code} - {stock_name} 的5日资金流数据:")
        
        # 获取5日资金流数据
        df_money = qs.stock_money(stock_code, ndays=[5])
        print(df_money)
        
        # 可视化资金流数据
        from qstock import plot
        plot.line(df_money, title=f"{stock_name}资金流")